import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { getSocketClient } from '@/lib/socket-client'

// POST /api/admin/live-quiz/sessions/[id]/start - Start live quiz session
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params, user }) => {
    const resolvedParams = await params
    const sessionId = resolvedParams?.id as string

    if (!sessionId) {
      return APIResponse.error('Session ID is required', 400)
    }

    try {
      // Get session with quiz details
      const session = await prisma.liveQuizSession.findUnique({
        where: { id: sessionId },
        include: {
          quiz: {
            include: {
              questions: {
                select: { id: true, order: true },
                orderBy: { order: 'asc' }
              }
            }
          },
          participants: {
            include: {
              user: {
                select: { id: true, name: true }
              }
            }
          }
        }
      })

      if (!session) {
        return APIResponse.error('Live quiz session not found', 404, 'SESSION_NOT_FOUND')
      }

      // Check if session can be started
      if (session.status === 'ACTIVE') {
        return APIResponse.error('Session is already active', 400, 'SESSION_ALREADY_ACTIVE')
      }

      if (session.status === 'COMPLETED') {
        return APIResponse.error('Cannot start completed session', 400, 'SESSION_COMPLETED')
      }

      if (session.quiz.questions.length === 0) {
        return APIResponse.error('Quiz has no questions', 400, 'NO_QUESTIONS')
      }

      // Update session status to ACTIVE
      const updatedSession = await prisma.liveQuizSession.update({
        where: { id: sessionId },
        data: {
          status: 'ACTIVE',
          startTime: new Date(),
          currentQuestion: 0
        },
        include: {
          quiz: {
            include: {
              questions: {
                select: {
                  id: true,
                  type: true,
                  text: true,
                  options: true,
                  points: true,
                  order: true
                },
                orderBy: { order: 'asc' }
              }
            }
          },
          participants: {
            include: {
              user: {
                select: { id: true, name: true, email: true }
              }
            }
          }
        }
      })

      // Reset all participants' progress
      await prisma.liveQuizParticipant.updateMany({
        where: { sessionId },
        data: {
          currentQuestion: 0,
          score: 0,
          correctAnswers: 0,
          totalAnswered: 0,
          rank: null,
          answers: {},
          timeSpent: 0
        }
      })

      // Broadcast session started event to all participants
      try {
        const socketClient = getSocketClient()
        
        // Notify all participants in the session room
        socketClient.emit('live-quiz:session-started', {
          sessionId,
          session: {
            id: updatedSession.id,
            title: updatedSession.title,
            status: updatedSession.status,
            currentQuestion: updatedSession.currentQuestion,
            questionTimeLimit: updatedSession.questionTimeLimit,
            autoAdvance: updatedSession.autoAdvance,
            showLeaderboard: updatedSession.showLeaderboard
          },
          quiz: {
            id: updatedSession.quiz.id,
            title: updatedSession.quiz.title,
            totalQuestions: updatedSession.quiz.questions.length
          },
          currentQuestionData: updatedSession.quiz.questions[0] || null
        })

        // Send individual notifications to participants
        for (const participant of updatedSession.participants) {
          if (participant.isActive) {
            socketClient.emit('notification:received', {
              userId: participant.userId,
              type: 'info',
              title: 'Live Quiz Started!',
              message: `${updatedSession.title} has started. Good luck!`,
              data: {
                sessionId,
                action: 'session-started'
              }
            })
          }
        }
      } catch (socketError) {
        console.warn('Failed to send socket notifications:', socketError)
        // Continue execution even if socket notifications fail
      }

      return APIResponse.success({
        session: {
          ...updatedSession,
          participantCount: updatedSession.participants.length,
          questionCount: updatedSession.quiz.questions.length
        },
        currentQuestion: updatedSession.quiz.questions[0] || null
      }, 'Live quiz session started successfully')

    } catch (error) {
      console.error('Error starting live quiz session:', error)
      return APIResponse.error('Failed to start live quiz session', 500)
    }
  }
)
